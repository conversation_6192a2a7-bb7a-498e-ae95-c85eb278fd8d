# RAG系统向量存储迁移PRD

## 1. 项目背景

### 1.1 当前状况
本项目是一个基于FastAPI + LlamaIndex构建的RAG（检索增强生成）聊天应用，目前使用ChromaDB + SQLite作为向量存储方案。系统具备以下特点：

- **技术栈**：FastAPI + LlamaIndex + ChromaDB + SQLite
- **存储方式**：本地SQLite文件存储向量数据
- **检索方式**：BM25关键词检索 + 向量相似度检索的混合检索
- **向量模型**：text-embedding-3-small（1536维）
- **数据规模**：支持文档上传、CMS文章同步等功能

### 1.2 现有架构分析
```
当前架构：
FastAPI应用 → RAGService → ChromaVectorStore → ChromaDB → SQLite文件
```

**优势**：
- 部署简单，无需外部服务
- 数据本地化，便于备份
- 开发调试方便

**局限性**：
- 性能瓶颈：SQLite在大规模向量检索时性能有限
- 扩展性差：难以支持分布式部署
- 功能受限：缺乏高级向量搜索功能
- 并发限制：SQLite的并发写入能力有限

## 2. 迁移目标

### 2.1 业务目标
- **性能提升**：提高向量检索查询响应速度
- **扩展性增强**：为未来大规模数据和分布式部署做准备
- **功能丰富**：利用Qdrant的高级功能（如混合搜索、过滤等）
- **生态完善**：接入更成熟的向量数据库生态系统

### 2.2 技术目标
- **零停机迁移**：最小化服务中断时间
- **数据完整性**：确保所有向量数据无损迁移
- **向后兼容**：保持现有API接口不变
- **性能基准**：查询响应时间不劣于现有系统

### 2.3 成功标准
- [ ] 所有现有功能正常工作
- [ ] 向量数据100%成功迁移
- [ ] 查询响应时间≤现有系统的120%
- [ ] 系统稳定运行7天无异常
- [ ] 前端用户体验无变化

## 3. 技术方案

### 3.1 目标架构
```
新架构：
FastAPI应用 → RAGService → QdrantVectorStore → Qdrant服务器
```

### 3.2 核心变更

#### 3.2.1 依赖包变更
**新增依赖**：
```
qdrant-client>=1.7.0
llama-index-vector-stores-qdrant
```

**保留依赖**：
```
llama-index-vector-stores-chroma  # 用于数据迁移
chromadb==1.0.12                 # 用于数据迁移
```

#### 3.2.2 配置文件变更
**backend/config/settings.py**：
```python
# 新增Qdrant配置
qdrant_host: str = "localhost"
qdrant_port: int = 6333
qdrant_grpc_port: int = 6334
qdrant_api_key: Optional[str] = None
qdrant_prefer_grpc: bool = True

# 向量存储类型选择
vector_store_type: str = "qdrant"  # "chroma" or "qdrant"
```

**.env文件**：
```env
# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_API_KEY=
QDRANT_PREFER_GRPC=true

# 向量存储类型
VECTOR_STORE_TYPE=qdrant
```

#### 3.2.3 服务类修改
**backend/app/services/rag_service.py**：
- 新增Qdrant客户端初始化逻辑
- 保持LlamaIndex接口统一
- 支持配置切换（Chroma/Qdrant）

### 3.3 部署要求

#### 3.3.1 Qdrant服务器部署
**Docker Compose方式（推荐）**：
```yaml
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant
    ports:
      - "6333:6333"  # HTTP API
      - "6334:6334"  # gRPC API
    volumes:
      - ./qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
```

**本地安装方式**：
```bash
# 使用Docker直接运行
docker run -p 6333:6333 -p 6334:6334 \
  -v $(pwd)/qdrant_data:/qdrant/storage \
  qdrant/qdrant:latest
```

## 4. 实施计划

### 4.1 阶段划分

#### 阶段1：准备阶段（1-2天）
**目标**：完成环境准备和方案设计
**任务**：
- [ ] 研究Qdrant部署最佳实践
- [ ] 准备Docker Compose配置文件
- [ ] 设计数据迁移策略
- [ ] 更新项目文档

**交付物**：
- Docker Compose配置文件
- 数据迁移方案设计文档
- 更新的README.md

#### 阶段2：开发阶段（2-3天）
**目标**：完成代码修改和工具开发
**任务**：
- [ ] 修改配置文件（settings.py, .env）
- [ ] 更新RAGService支持Qdrant
- [ ] 编写数据迁移脚本
- [ ] 更新诊断和维护脚本
- [ ] 更新requirements.txt

**交付物**：
- 修改后的配置文件
- 更新的RAGService代码
- 数据迁移脚本
- 更新的诊断脚本

#### 阶段3：测试阶段（1-2天）
**目标**：验证功能完整性和性能
**任务**：
- [ ] 功能测试（文档上传、查询、CMS同步）
- [ ] 性能基准测试
- [ ] 数据一致性验证
- [ ] 错误处理测试
- [ ] 回滚流程测试

**交付物**：
- 测试报告
- 性能对比数据
- 问题修复记录

#### 阶段4：部署阶段（1天）
**目标**：生产环境迁移
**任务**：
- [ ] 备份现有数据
- [ ] 部署Qdrant服务器
- [ ] 执行数据迁移
- [ ] 切换到新系统
- [ ] 监控和验证

**交付物**：
- 部署记录
- 迁移日志
- 验证报告

### 4.2 时间安排
**总计**：5-8个工作日
- 准备阶段：1-2天
- 开发阶段：2-3天  
- 测试阶段：1-2天
- 部署阶段：1天

### 4.3 人员安排
- **开发工程师**：负责代码修改和脚本开发
- **运维工程师**：负责Qdrant服务器部署和监控
- **测试工程师**：负责功能和性能测试

## 5. 风险评估与应对

### 5.1 主要风险

#### 5.1.1 数据丢失风险
**风险描述**：迁移过程中向量数据可能丢失或损坏
**影响程度**：高
**应对措施**：
- 完整备份现有storage目录
- 分批迁移，验证每批数据
- 保留原系统作为备份
- 提供快速回滚机制

#### 5.1.2 服务中断风险
**风险描述**：迁移期间服务不可用
**影响程度**：中
**应对措施**：
- 选择低峰时段进行迁移
- 准备维护页面
- 控制迁移时间在2小时内
- 提供紧急回滚方案

#### 5.1.3 性能下降风险
**风险描述**：新系统性能不如预期
**影响程度**：中
**应对措施**：
- 充分的性能测试
- Qdrant参数调优
- 监控关键性能指标
- 准备性能优化方案

#### 5.1.4 兼容性问题
**风险描述**：LlamaIndex版本兼容性问题
**影响程度**：低
**应对措施**：
- 测试环境先行验证
- 锁定依赖包版本
- 准备降级方案

### 5.2 回滚方案
**触发条件**：
- 数据迁移失败
- 系统功能异常
- 性能严重下降
- 用户反馈重大问题

**回滚步骤**：
1. 停止新系统服务
2. 恢复原配置文件
3. 重启原ChromaDB系统
4. 验证功能正常
5. 通知相关人员

**回滚时间**：≤30分钟

## 6. 监控与验证

### 6.1 关键指标
- **功能指标**：文档上传成功率、查询响应成功率
- **性能指标**：查询响应时间、并发处理能力
- **稳定性指标**：系统可用性、错误率
- **数据指标**：向量数据完整性、元数据一致性

### 6.2 监控方案
- **实时监控**：API响应时间、错误率
- **定期检查**：数据完整性验证
- **用户反馈**：前端功能异常报告
- **日志分析**：系统运行日志分析

## 7. 预期收益

### 7.1 性能收益
- **查询速度**：预期提升20-50%
- **并发能力**：支持更高并发查询
- **扩展性**：支持水平扩展

### 7.2 功能收益
- **高级搜索**：支持更复杂的过滤条件
- **混合搜索**：更好的BM25+向量混合检索
- **监控能力**：更丰富的监控和管理功能

### 7.3 运维收益
- **云服务**：可选择Qdrant Cloud托管服务
- **备份恢复**：更完善的备份和恢复机制
- **集群管理**：支持分布式部署

## 8. 后续规划

### 8.1 短期优化（1-2周）
- 性能调优和参数优化
- 监控告警完善
- 文档和培训材料更新

### 8.2 中期扩展（1-3个月）
- 评估Qdrant Cloud迁移
- 实现分布式部署
- 添加高级搜索功能

### 8.3 长期发展（3-6个月）
- 多模态向量搜索
- 实时数据同步
- 智能推荐功能

---

**文档版本**：v1.0  
**创建日期**：2025-01-07  
**最后更新**：2025-01-07  
**负责人**：开发团队
